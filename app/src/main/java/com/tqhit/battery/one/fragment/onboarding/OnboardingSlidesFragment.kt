package com.tqhit.battery.one.fragment.onboarding

import android.content.Intent
import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import androidx.fragment.app.activityViewModels
import androidx.navigation.fragment.findNavController
import com.tqhit.adlib.sdk.base.ui.AdLibBaseFragment
import com.tqhit.battery.one.R
import com.tqhit.battery.one.activity.main.MainActivity
import com.tqhit.battery.one.activity.onboarding.OnboardingActivity
import com.tqhit.battery.one.databinding.FragmentOnboardingSlidesBinding
import com.tqhit.battery.one.viewmodel.AppViewModel
import dagger.hilt.android.AndroidEntryPoint

/**
 * Onboarding Slides Fragment that wraps the existing onboarding slide functionality.
 * 
 * This fragment serves as a bridge between the new fragment-based onboarding flow
 * and the existing StartingActivity slide implementation. It provides a way to
 * integrate the existing onboarding slides into the new Navigation Component flow.
 * 
 * Features:
 * - Wraps existing onboarding slide logic
 * - Integrates with Navigation Component
 * - Handles completion and navigation to MainActivity
 * - Maintains compatibility with existing onboarding functionality
 * - Proper lifecycle management and state handling
 * 
 * Navigation Flow: Language Selection → Onboarding Slides → Main App
 */
@AndroidEntryPoint
class OnboardingSlidesFragment : AdLibBaseFragment<FragmentOnboardingSlidesBinding>() {

    companion object {
        private const val TAG = "OnboardingSlidesFragment"
        
        fun newInstance(): OnboardingSlidesFragment {
            return OnboardingSlidesFragment()
        }
    }

    // ViewBinding implementation following established pattern
    override val binding by lazy {
        Log.d(TAG, "BINDING_INIT: Initializing FragmentOnboardingSlidesBinding")
        try {
            FragmentOnboardingSlidesBinding.inflate(layoutInflater)
        } catch (e: Exception) {
            Log.e(TAG, "BINDING_INIT: Critical error initializing ViewBinding", e)
            throw e
        }
    }

    // ViewModels
    private val appViewModel: AppViewModel by activityViewModels()

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        val startTime = System.currentTimeMillis()
        Log.d(TAG, "STARTUP_TIMING: OnboardingSlidesFragment.onViewCreated() started at $startTime")

        super.onViewCreated(view, savedInstanceState)

        // Setup the embedded onboarding content
        safeBindingAccess("setupOnboardingContent") { binding ->
            val setupStartTime = System.currentTimeMillis()
            setupOnboardingContent()
            Log.d(TAG, "STARTUP_TIMING: Onboarding content setup took ${System.currentTimeMillis() - setupStartTime}ms")
        }

        Log.d(TAG, "STARTUP_TIMING: OnboardingSlidesFragment.onViewCreated() completed in ${System.currentTimeMillis() - startTime}ms")
    }

    /**
     * Sets up the onboarding content.
     * For now, this provides a simple completion button.
     * In a full implementation, this would embed the existing StartingActivity slides.
     */
    private fun setupOnboardingContent() {
        safeBindingAccess("setupOnboardingContent") { binding ->
            // Setup completion button
            binding.completeOnboardingButton.setOnClickListener {
                completeOnboarding()
            }
            
            // Setup skip button (optional)
            binding.skipOnboardingButton.setOnClickListener {
                completeOnboarding()
            }
            
            Log.d(TAG, "ONBOARDING_SLIDES: Content setup completed")
        }
    }

    /**
     * Completes the onboarding process and navigates to MainActivity.
     */
    private fun completeOnboarding() {
        Log.d(TAG, "ONBOARDING_SLIDES: Completing onboarding process")
        
        try {
            // Mark onboarding as completed
            appViewModel.setShowedStartPage(true)
            Log.d(TAG, "ONBOARDING_SLIDES: Onboarding completion flag set")
            
            // Navigate to MainActivity
            navigateToMainActivity()
            
        } catch (e: Exception) {
            Log.e(TAG, "ONBOARDING_SLIDES: Error completing onboarding", e)
            // Fallback navigation
            fallbackToMainActivity()
        }
    }

    /**
     * Navigates to MainActivity using the parent OnboardingActivity.
     */
    private fun navigateToMainActivity() {
        try {
            val parentActivity = requireActivity()
            if (parentActivity is OnboardingActivity) {
                parentActivity.navigateToMainActivity()
                Log.d(TAG, "ONBOARDING_SLIDES: Navigation delegated to OnboardingActivity")
            } else {
                // Direct navigation if not in OnboardingActivity
                fallbackToMainActivity()
            }
        } catch (e: Exception) {
            Log.e(TAG, "ONBOARDING_SLIDES: Error in navigation delegation", e)
            fallbackToMainActivity()
        }
    }

    /**
     * Fallback navigation directly to MainActivity.
     */
    private fun fallbackToMainActivity() {
        try {
            val intent = Intent(requireContext(), MainActivity::class.java)
            intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
            startActivity(intent)
            requireActivity().finish()
            
            Log.d(TAG, "ONBOARDING_SLIDES: Fallback navigation to MainActivity completed")
        } catch (e: Exception) {
            Log.e(TAG, "ONBOARDING_SLIDES: Error in fallback navigation", e)
        }
    }

    /**
     * Safe binding access method with error handling.
     * Follows the established pattern from other fragments.
     */
    private fun safeBindingAccess(operation: String, action: (FragmentOnboardingSlidesBinding) -> Unit) {
        try {
            if (isAdded && !isDetached && view != null) {
                action(binding)
            } else {
                Log.w(TAG, "BINDING_ACCESS: Skipping $operation - fragment not ready or not attached")
            }
        } catch (e: Exception) {
            Log.e(TAG, "BINDING_ACCESS: Error in $operation", e)
        }
    }
}
