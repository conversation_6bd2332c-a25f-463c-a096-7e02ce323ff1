package com.tqhit.battery.one.activity.splash

import android.content.Intent
import android.os.Bundle
import android.util.Log
import androidx.activity.viewModels
import androidx.core.splashscreen.SplashScreen.Companion.installSplashScreen
import com.tqhit.adlib.sdk.base.ui.AdLibBaseActivity
import com.tqhit.battery.one.activity.main.MainActivity
import com.tqhit.battery.one.activity.onboarding.OnboardingActivity
import com.tqhit.battery.one.activity.starting.StartingActivity
import com.tqhit.battery.one.databinding.ActivitySplashBinding
import com.tqhit.battery.one.utils.DeviceUtils
import com.tqhit.battery.one.viewmodel.AppViewModel
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class SplashActivity : AdLibBaseActivity<ActivitySplashBinding>() {
    override val binding by lazy { ActivitySplashBinding.inflate(layoutInflater) }

    private val appViewModel: AppViewModel by viewModels()
    
    companion object {
        private const val TAG = "SplashActivity"
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        val startTime = System.currentTimeMillis()
        Log.d(TAG, "STARTUP_TIMING: SplashActivity.onCreate() started at $startTime")

        // Apply device-specific adjustments before anything else
        val adjustmentsStartTime = System.currentTimeMillis()
        applyDeviceSpecificAdjustments()
        Log.d(TAG, "STARTUP_TIMING: Device adjustments took ${System.currentTimeMillis() - adjustmentsStartTime}ms")

        val splashStartTime = System.currentTimeMillis()
        installSplashScreen()
        Log.d(TAG, "STARTUP_TIMING: Splash screen installation took ${System.currentTimeMillis() - splashStartTime}ms")

        val superStartTime = System.currentTimeMillis()
        super.onCreate(savedInstanceState)
        Log.d(TAG, "STARTUP_TIMING: super.onCreate() took ${System.currentTimeMillis() - superStartTime}ms")

        Log.d(TAG, "STARTUP_TIMING: SplashActivity.onCreate() completed in ${System.currentTimeMillis() - startTime}ms")
    }
    
    /**
     * Apply device-specific adjustments to ensure compatibility
     */
    private fun applyDeviceSpecificAdjustments() {
        try {
            Log.d(TAG, "Applying device-specific adjustments")
            DeviceUtils.applyDeviceSpecificAdjustments(this)
            
            // Check for Xiaomi device
            if (DeviceUtils.isXiaomiDevice()) {
                Log.d(TAG, "Xiaomi device detected, applying additional settings")
                
                // Use appropriate hardware acceleration settings for MIUI
                try {
                    window?.setFlags(
                        android.view.WindowManager.LayoutParams.FLAG_HARDWARE_ACCELERATED,
                        android.view.WindowManager.LayoutParams.FLAG_HARDWARE_ACCELERATED
                    )
                    
                    // Also set specific layer types for better compatibility
                    binding.root.setLayerType(android.view.View.LAYER_TYPE_HARDWARE, null)
                } catch (e: Exception) {
                    Log.e(TAG, "Error setting hardware acceleration flags", e)
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error applying device adjustments", e)
        }
    }

    override fun setupData() {
        val startTime = System.currentTimeMillis()
        Log.d(TAG, "STARTUP_TIMING: SplashActivity.setupData() started at $startTime")

        super.setupData()

        val navigationStartTime = System.currentTimeMillis()
        if (appViewModel.isShowedStartPage()) {
            Log.d(TAG, "STARTUP_TIMING: User has completed onboarding - navigating to MainActivity")
            startActivity(Intent(this, MainActivity::class.java))
        } else {
            Log.d(TAG, "STARTUP_TIMING: First time user - navigating to OnboardingActivity with Language Selection")
            val intent = Intent(this, OnboardingActivity::class.java)
            intent.putExtra(OnboardingActivity.EXTRA_START_WITH_LANGUAGE_SELECTION, true)
            startActivity(intent)
        }
        Log.d(TAG, "STARTUP_TIMING: Navigation took ${System.currentTimeMillis() - navigationStartTime}ms")

        finish()
        Log.d(TAG, "STARTUP_TIMING: SplashActivity.setupData() completed in ${System.currentTimeMillis() - startTime}ms")
    }
}
