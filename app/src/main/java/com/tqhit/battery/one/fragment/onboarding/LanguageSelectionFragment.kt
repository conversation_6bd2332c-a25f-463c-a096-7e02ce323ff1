package com.tqhit.battery.one.fragment.onboarding

import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import androidx.fragment.app.activityViewModels
import androidx.fragment.app.viewModels
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.repeatOnLifecycle
import androidx.navigation.fragment.findNavController
import com.tqhit.adlib.sdk.base.ui.AdLibBaseFragment
import com.tqhit.battery.one.R
import com.tqhit.battery.one.databinding.FragmentLanguageSelectionBinding
import com.tqhit.battery.one.viewmodel.AppViewModel
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.launch

/**
 * Language Selection Fragment for the onboarding flow.
 * 
 * Features:
 * - Full-screen language selection interface adapted from dialog layout
 * - MVI pattern state management with proper ViewBinding
 * - Language selection logic reused from SelectLanguageDialog
 * - Automatic navigation to next onboarding step after language selection
 * - Material 3 design consistency with onboarding flow
 * - Proper fragment lifecycle management and state persistence
 * 
 * Navigation Flow: Splash → Language Selection → Onboarding Slides → Main App
 */
@AndroidEntryPoint
class LanguageSelectionFragment : AdLibBaseFragment<FragmentLanguageSelectionBinding>() {

    companion object {
        private const val TAG = "LanguageSelectionFragment"
        
        fun newInstance(): LanguageSelectionFragment {
            return LanguageSelectionFragment()
        }
    }

    // ViewBinding implementation following established pattern
    override val binding by lazy {
        Log.d(TAG, "BINDING_INIT: Initializing FragmentLanguageSelectionBinding")
        try {
            FragmentLanguageSelectionBinding.inflate(layoutInflater)
        } catch (e: Exception) {
            Log.e(TAG, "BINDING_INIT: Critical error initializing ViewBinding", e)
            throw e
        }
    }

    // ViewModels
    private val appViewModel: AppViewModel by activityViewModels()
    private val languageSelectionViewModel: LanguageSelectionViewModel by viewModels()

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        val startTime = System.currentTimeMillis()
        Log.d(TAG, "STARTUP_TIMING: LanguageSelectionFragment.onViewCreated() started at $startTime")

        super.onViewCreated(view, savedInstanceState)

        // Setup UI components
        safeBindingAccess("setupUI") { binding ->
            val setupStartTime = System.currentTimeMillis()
            setupLanguageButtons()
            updateSelectedLanguageUI()
            Log.d(TAG, "STARTUP_TIMING: UI setup took ${System.currentTimeMillis() - setupStartTime}ms")
        }

        // Observe ViewModel state
        observeLanguageSelectionState()

        Log.d(TAG, "STARTUP_TIMING: LanguageSelectionFragment.onViewCreated() completed in ${System.currentTimeMillis() - startTime}ms")
    }

    /**
     * Sets up click listeners for all language option buttons.
     * Reuses the same language codes and logic from SelectLanguageDialog.
     */
    private fun setupLanguageButtons() {
        safeBindingAccess("setupLanguageButtons") { binding ->
            // Setup click listeners for each language option
            binding.de.setOnClickListener { selectLanguage("de") } // German
            binding.nl.setOnClickListener { selectLanguage("nl") } // Dutch
            binding.en.setOnClickListener { selectLanguage("en") } // English
            binding.es.setOnClickListener { selectLanguage("es") } // Spanish
            binding.fr.setOnClickListener { selectLanguage("fr") } // French
            binding.it.setOnClickListener { selectLanguage("it") } // Italian
            binding.hu.setOnClickListener { selectLanguage("hu") } // Hungarian
            binding.pl.setOnClickListener { selectLanguage("pl") } // Polish
            binding.pt.setOnClickListener { selectLanguage("pt") } // Portuguese
            binding.ro.setOnClickListener { selectLanguage("ro") } // Romanian
            binding.tr.setOnClickListener { selectLanguage("tr") } // Turkish
            binding.ru.setOnClickListener { selectLanguage("ru") } // Russian
            binding.ua.setOnClickListener { selectLanguage("uk") } // Ukrainian
            binding.ar.setOnClickListener { selectLanguage("ar") } // Arabic
            binding.zh.setOnClickListener { selectLanguage("zh") } // Chinese
            
            Log.d(TAG, "LANGUAGE_SELECTION: Language button listeners setup completed")
        }
    }

    /**
     * Updates the UI to show the currently selected language.
     * Applies the same visual selection logic as SelectLanguageDialog.
     */
    private fun updateSelectedLanguageUI() {
        safeBindingAccess("updateSelectedLanguageUI") { binding ->
            val currentLanguage = appViewModel.getLanguage()
            Log.d(TAG, "LANGUAGE_SELECTION: Updating UI for current language: $currentLanguage")

            // Set backgrounds and selection state based on current language
            binding.de.apply {
                isSelected = currentLanguage == "de"
                setBackgroundResource(if (isSelected) R.drawable.grey_block_selected_line_up else R.drawable.grey_block_line_up)
            }

            binding.nl.apply {
                isSelected = currentLanguage == "nl"
                setBackgroundResource(if (isSelected) R.drawable.grey_block_selected_color else R.drawable.grey_block_line)
            }

            binding.en.apply {
                isSelected = currentLanguage == "en"
                setBackgroundResource(if (isSelected) R.drawable.grey_block_selected_color else R.drawable.grey_block_line)
            }

            binding.es.apply {
                isSelected = currentLanguage == "es"
                setBackgroundResource(if (isSelected) R.drawable.grey_block_selected_color else R.drawable.grey_block_line)
            }

            binding.fr.apply {
                isSelected = currentLanguage == "fr"
                setBackgroundResource(if (isSelected) R.drawable.grey_block_selected_color else R.drawable.grey_block_line)
            }

            binding.it.apply {
                isSelected = currentLanguage == "it"
                setBackgroundResource(if (isSelected) R.drawable.grey_block_selected_color else R.drawable.grey_block_line)
            }

            binding.hu.apply {
                isSelected = currentLanguage == "hu"
                setBackgroundResource(if (isSelected) R.drawable.grey_block_selected_color else R.drawable.grey_block_line)
            }

            binding.pl.apply {
                isSelected = currentLanguage == "pl"
                setBackgroundResource(if (isSelected) R.drawable.grey_block_selected_color else R.drawable.grey_block_line)
            }

            binding.pt.apply {
                isSelected = currentLanguage == "pt"
                setBackgroundResource(if (isSelected) R.drawable.grey_block_selected_color else R.drawable.grey_block_line)
            }

            binding.ro.apply {
                isSelected = currentLanguage == "ro"
                setBackgroundResource(if (isSelected) R.drawable.grey_block_selected_color else R.drawable.grey_block_line)
            }

            binding.tr.apply {
                isSelected = currentLanguage == "tr"
                setBackgroundResource(if (isSelected) R.drawable.grey_block_selected_color else R.drawable.grey_block_line)
            }

            binding.ru.apply {
                isSelected = currentLanguage == "ru"
                setBackgroundResource(if (isSelected) R.drawable.grey_block_selected_color else R.drawable.grey_block_line)
            }

            binding.ua.apply {
                isSelected = currentLanguage == "uk"
                setBackgroundResource(if (isSelected) R.drawable.grey_block_selected_color else R.drawable.grey_block_line)
            }

            binding.ar.apply {
                isSelected = currentLanguage == "ar"
                setBackgroundResource(if (isSelected) R.drawable.grey_block_selected_color else R.drawable.grey_block_line)
            }

            binding.zh.apply {
                isSelected = currentLanguage == "zh"
                setBackgroundResource(if (isSelected) R.drawable.grey_block_selected_line_down else R.drawable.grey_block_line_down)
            }
        }
    }

    /**
     * Handles language selection and triggers navigation to next onboarding step.
     */
    private fun selectLanguage(languageCode: String) {
        Log.d(TAG, "LANGUAGE_SELECTION: User selected language: $languageCode")
        
        // Trigger language selection through ViewModel
        languageSelectionViewModel.selectLanguage(languageCode)
    }

    /**
     * Observes language selection state and handles navigation.
     */
    private fun observeLanguageSelectionState() {
        viewLifecycleOwner.lifecycleScope.launch {
            repeatOnLifecycle(Lifecycle.State.STARTED) {
                languageSelectionViewModel.uiState.collect { state ->
                    when (state) {
                        is LanguageSelectionUiState.Idle -> {
                            Log.d(TAG, "LANGUAGE_SELECTION: State - Idle")
                        }
                        is LanguageSelectionUiState.LanguageSelected -> {
                            Log.d(TAG, "LANGUAGE_SELECTION: State - Language selected: ${state.languageCode}")
                            handleLanguageSelected(state.languageCode)
                        }
                        is LanguageSelectionUiState.NavigatingToOnboarding -> {
                            Log.d(TAG, "LANGUAGE_SELECTION: State - Navigating to onboarding")
                            navigateToOnboarding()
                        }
                    }
                }
            }
        }
    }

    /**
     * Handles the language selection completion.
     */
    private fun handleLanguageSelected(languageCode: String) {
        // Save language using AppViewModel
        appViewModel.setLanguage(languageCode)
        
        // Apply the new language
        appViewModel.setLocale(requireContext(), languageCode)
        
        // Update UI to reflect selection
        updateSelectedLanguageUI()
        
        // Trigger navigation after a short delay for visual feedback
        languageSelectionViewModel.proceedToOnboarding()
        
        Log.d(TAG, "LANGUAGE_SELECTION: Language $languageCode saved and applied")
    }

    /**
     * Navigates to the next step in the onboarding flow.
     */
    private fun navigateToOnboarding() {
        try {
            // Navigate to onboarding slides
            findNavController().navigate(R.id.action_languageSelection_to_onboarding)
            Log.d(TAG, "LANGUAGE_SELECTION: Navigation to onboarding completed")
        } catch (e: Exception) {
            Log.e(TAG, "LANGUAGE_SELECTION: Error navigating to onboarding", e)
            // Fallback: finish the current activity to return to splash
            requireActivity().finish()
        }
    }

    /**
     * Safe binding access method with error handling.
     * Follows the established pattern from other fragments.
     */
    private fun safeBindingAccess(operation: String, action: (FragmentLanguageSelectionBinding) -> Unit) {
        try {
            if (isAdded && !isDetached && view != null) {
                action(binding)
            } else {
                Log.w(TAG, "BINDING_ACCESS: Skipping $operation - fragment not ready or not attached")
            }
        } catch (e: Exception) {
            Log.e(TAG, "BINDING_ACCESS: Error in $operation", e)
        }
    }
}
