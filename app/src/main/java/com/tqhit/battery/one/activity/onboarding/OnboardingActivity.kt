package com.tqhit.battery.one.activity.onboarding

import android.content.Intent
import android.os.Bundle
import android.util.Log
import androidx.activity.viewModels
import androidx.navigation.NavController
import androidx.navigation.fragment.NavHostFragment
import androidx.navigation.ui.setupActionBarWithNavController
import com.tqhit.adlib.sdk.base.ui.AdLibBaseActivity
import com.tqhit.battery.one.R
import com.tqhit.battery.one.activity.main.MainActivity
import com.tqhit.battery.one.activity.starting.StartingActivity
import com.tqhit.battery.one.databinding.ActivityOnboardingBinding
import com.tqhit.battery.one.viewmodel.AppViewModel
import dagger.hilt.android.AndroidEntryPoint

/**
 * Onboarding Activity that manages the navigation flow for new users.
 * 
 * Navigation Flow:
 * 1. Language Selection Fragment (new) - User selects preferred language
 * 2. Existing Onboarding Slides - Privacy policy, permissions, etc.
 * 3. Main App - Navigate to MainActivity after completion
 * 
 * Features:
 * - Fragment-based navigation using Navigation Component
 * - Language selection integration with existing onboarding flow
 * - Proper state management and lifecycle handling
 * - Material 3 design consistency
 * - Comprehensive logging for debugging and ADB testing
 * 
 * Architecture:
 * - Uses Navigation Component for fragment transitions
 * - Integrates with existing StartingActivity slides
 * - Maintains compatibility with current onboarding logic
 * - Follows established activity patterns in the project
 */
@AndroidEntryPoint
class OnboardingActivity : AdLibBaseActivity<ActivityOnboardingBinding>() {

    companion object {
        private const val TAG = "OnboardingActivity"
        
        // Navigation arguments
        const val EXTRA_START_WITH_LANGUAGE_SELECTION = "start_with_language_selection"
        const val EXTRA_SKIP_LANGUAGE_SELECTION = "skip_language_selection"
    }

    override val binding by lazy { ActivityOnboardingBinding.inflate(layoutInflater) }

    private val appViewModel: AppViewModel by viewModels()
    private lateinit var navController: NavController

    override fun onCreate(savedInstanceState: Bundle?) {
        val startTime = System.currentTimeMillis()
        Log.d(TAG, "STARTUP_TIMING: OnboardingActivity.onCreate() started at $startTime")

        super.onCreate(savedInstanceState)

        setupNavigation()
        handleNavigationIntent()

        Log.d(TAG, "STARTUP_TIMING: OnboardingActivity.onCreate() completed in ${System.currentTimeMillis() - startTime}ms")
    }

    /**
     * Sets up the Navigation Component for fragment navigation.
     */
    private fun setupNavigation() {
        try {
            val navHostFragment = supportFragmentManager
                .findFragmentById(R.id.nav_host_fragment_onboarding) as NavHostFragment
            navController = navHostFragment.navController

            Log.d(TAG, "ONBOARDING_NAV: Navigation setup completed")
        } catch (e: Exception) {
            Log.e(TAG, "ONBOARDING_NAV: Error setting up navigation", e)
            // Fallback to StartingActivity if navigation setup fails
            fallbackToStartingActivity()
        }
    }

    /**
     * Handles navigation based on intent extras.
     */
    private fun handleNavigationIntent() {
        val startWithLanguageSelection = intent.getBooleanExtra(EXTRA_START_WITH_LANGUAGE_SELECTION, true)
        val skipLanguageSelection = intent.getBooleanExtra(EXTRA_SKIP_LANGUAGE_SELECTION, false)

        Log.d(TAG, "ONBOARDING_NAV: Intent - startWithLanguageSelection: $startWithLanguageSelection, skipLanguageSelection: $skipLanguageSelection")

        when {
            skipLanguageSelection -> {
                // Skip language selection and go directly to onboarding slides
                navigateToOnboardingSlides()
            }
            startWithLanguageSelection -> {
                // Start with language selection (default behavior)
                // Navigation graph already starts with language selection fragment
                Log.d(TAG, "ONBOARDING_NAV: Starting with language selection")
            }
            else -> {
                // Fallback to existing onboarding flow
                fallbackToStartingActivity()
            }
        }
    }

    /**
     * Navigates directly to onboarding slides, skipping language selection.
     */
    private fun navigateToOnboardingSlides() {
        try {
            navController.navigate(R.id.action_languageSelection_to_onboarding)
            Log.d(TAG, "ONBOARDING_NAV: Navigated directly to onboarding slides")
        } catch (e: Exception) {
            Log.e(TAG, "ONBOARDING_NAV: Error navigating to onboarding slides", e)
            fallbackToStartingActivity()
        }
    }

    /**
     * Fallback to the existing StartingActivity if navigation fails.
     */
    private fun fallbackToStartingActivity() {
        Log.w(TAG, "ONBOARDING_NAV: Falling back to StartingActivity")
        try {
            val intent = Intent(this, StartingActivity::class.java)
            startActivity(intent)
            finish()
        } catch (e: Exception) {
            Log.e(TAG, "ONBOARDING_NAV: Error falling back to StartingActivity", e)
            // Last resort: go to MainActivity
            navigateToMainActivity()
        }
    }

    /**
     * Navigates to MainActivity after onboarding completion.
     */
    fun navigateToMainActivity() {
        Log.d(TAG, "ONBOARDING_NAV: Navigating to MainActivity")
        try {
            // Mark onboarding as completed
            appViewModel.setShowedStartPage(true)
            
            val intent = Intent(this, MainActivity::class.java)
            intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
            startActivity(intent)
            finish()
            
            Log.d(TAG, "ONBOARDING_NAV: Successfully navigated to MainActivity")
        } catch (e: Exception) {
            Log.e(TAG, "ONBOARDING_NAV: Error navigating to MainActivity", e)
        }
    }



    /**
     * Handles system back button press.
     */
    override fun onBackPressed() {
        try {
            if (!navController.navigateUp()) {
                // If we can't navigate up, finish the activity
                Log.d(TAG, "ONBOARDING_NAV: Back pressed - finishing onboarding")
                super.onBackPressed()
            }
        } catch (e: Exception) {
            Log.e(TAG, "ONBOARDING_NAV: Error handling back press", e)
            super.onBackPressed()
        }
    }

    override fun setupData() {
        super.setupData()
        Log.d(TAG, "ONBOARDING_NAV: OnboardingActivity data setup completed")
    }

    override fun setupUI() {
        super.setupUI()
        Log.d(TAG, "ONBOARDING_NAV: OnboardingActivity UI setup completed")
    }

    override fun setupListener() {
        super.setupListener()
        Log.d(TAG, "ONBOARDING_NAV: OnboardingActivity listeners setup completed")
    }

    override fun onDestroy() {
        super.onDestroy()
        Log.d(TAG, "ONBOARDING_NAV: OnboardingActivity destroyed")
    }
}
