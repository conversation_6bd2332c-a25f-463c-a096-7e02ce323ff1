<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@color/white"
    tools:context=".activity.onboarding.OnboardingActivity">

    <!-- Navigation Host Fragment for Onboarding Flow -->
    <androidx.fragment.app.FragmentContainerView
        android:id="@+id/nav_host_fragment_onboarding"
        android:name="androidx.navigation.fragment.NavHostFragment"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:navGraph="@navigation/onboarding_nav_graph"
        app:defaultNavHost="true" />

</LinearLayout>
