<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="?attr/colorSurface"
    android:padding="16dp">

    <!-- Header Section -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:layout_marginBottom="24dp"
        android:gravity="center">
        
        <TextView
            android:id="@+id/titleText"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/select_language_title"
            android:textSize="24sp"
            android:textColor="?attr/black"
            android:textStyle="bold"
            android:gravity="center"
            android:layout_marginBottom="8dp"/>
            
        <TextView
            android:id="@+id/subtitleText"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/select_language_subtitle"
            android:textSize="16sp"
            android:textColor="?attr/black"
            android:alpha="0.7"
            android:gravity="center"/>
    </LinearLayout>

    <!-- Language Selection Content -->
    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:fillViewport="true">
        
        <LinearLayout
            android:orientation="vertical"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingBottom="16dp">
            
            <TextView
                android:textSize="14sp"
                android:textColor="?attr/black"
                android:id="@+id/de"
                android:background="@drawable/grey_block_line_up"
                android:paddingTop="12.5dp"
                android:paddingBottom="12.5dp"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="5dp"
                android:text="@string/Germany_lang"
                android:textAlignment="center"
                android:paddingVertical="12.5dp"/>
                
            <TextView
                android:textSize="14sp"
                android:textColor="?attr/black"
                android:id="@+id/nl"
                android:background="@drawable/grey_block_line"
                android:paddingLeft="12dp"
                android:paddingTop="12.5dp"
                android:paddingRight="12dp"
                android:paddingBottom="12.5dp"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="5dp"
                android:text="@string/Dutch_lang"
                android:textAlignment="center"
                android:paddingHorizontal="12dp"
                android:paddingVertical="12.5dp"/>
                
            <TextView
                android:textSize="14sp"
                android:textColor="?attr/black"
                android:id="@+id/en"
                android:background="@drawable/grey_block_line"
                android:paddingLeft="13dp"
                android:paddingTop="12.5dp"
                android:paddingRight="13dp"
                android:paddingBottom="12.5dp"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="5dp"
                android:text="@string/English_lang"
                android:textAlignment="center"
                android:paddingHorizontal="13dp"
                android:paddingVertical="12.5dp"/>
                
            <TextView
                android:textSize="14sp"
                android:textColor="?attr/black"
                android:id="@+id/es"
                android:background="@drawable/grey_block_line"
                android:paddingLeft="13dp"
                android:paddingTop="12.5dp"
                android:paddingRight="13dp"
                android:paddingBottom="12.5dp"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="5dp"
                android:text="@string/Spanish_lang"
                android:textAlignment="center"
                android:paddingHorizontal="13dp"
                android:paddingVertical="12.5dp"/>
                
            <TextView
                android:textSize="14sp"
                android:textColor="?attr/black"
                android:id="@+id/fr"
                android:background="@drawable/grey_block_line"
                android:paddingLeft="13dp"
                android:paddingTop="12.5dp"
                android:paddingRight="13dp"
                android:paddingBottom="12.5dp"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="5dp"
                android:text="@string/French_lang"
                android:textAlignment="center"
                android:paddingHorizontal="13dp"
                android:paddingVertical="12.5dp"/>
                
            <TextView
                android:textSize="14sp"
                android:textColor="?attr/black"
                android:id="@+id/it"
                android:background="@drawable/grey_block_line"
                android:paddingLeft="13dp"
                android:paddingTop="12.5dp"
                android:paddingRight="13dp"
                android:paddingBottom="12.5dp"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="5dp"
                android:text="@string/Italy_lang"
                android:textAlignment="center"
                android:paddingHorizontal="13dp"
                android:paddingVertical="12.5dp"/>
                
            <TextView
                android:textSize="14sp"
                android:textColor="?attr/black"
                android:id="@+id/hu"
                android:background="@drawable/grey_block_line"
                android:paddingLeft="13dp"
                android:paddingTop="12.5dp"
                android:paddingRight="13dp"
                android:paddingBottom="12.5dp"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="5dp"
                android:text="@string/Hungarian_lang"
                android:textAlignment="center"
                android:paddingHorizontal="13dp"
                android:paddingVertical="12.5dp"/>
                
            <TextView
                android:textSize="14sp"
                android:textColor="?attr/black"
                android:id="@+id/pl"
                android:background="@drawable/grey_block_line"
                android:paddingLeft="13dp"
                android:paddingTop="12.5dp"
                android:paddingRight="13dp"
                android:paddingBottom="12.5dp"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="5dp"
                android:text="@string/Poland_lang"
                android:textAlignment="center"
                android:paddingHorizontal="13dp"
                android:paddingVertical="12.5dp"/>
                
            <TextView
                android:textSize="14sp"
                android:textColor="?attr/black"
                android:id="@+id/pt"
                android:background="@drawable/grey_block_line"
                android:paddingLeft="13dp"
                android:paddingTop="12.5dp"
                android:paddingRight="13dp"
                android:paddingBottom="12.5dp"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="5dp"
                android:text="@string/Portuguese_lang"
                android:textAlignment="center"
                android:paddingHorizontal="13dp"
                android:paddingVertical="12.5dp"/>
                
            <TextView
                android:textSize="14sp"
                android:textColor="?attr/black"
                android:id="@+id/ro"
                android:background="@drawable/grey_block_line"
                android:paddingLeft="13dp"
                android:paddingTop="12.5dp"
                android:paddingRight="13dp"
                android:paddingBottom="12.5dp"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="5dp"
                android:text="@string/Romanian_lang"
                android:textAlignment="center"
                android:paddingHorizontal="13dp"
                android:paddingVertical="12.5dp"/>
                
            <TextView
                android:textSize="14sp"
                android:textColor="?attr/black"
                android:id="@+id/tr"
                android:background="@drawable/grey_block_line"
                android:paddingLeft="13dp"
                android:paddingTop="12.5dp"
                android:paddingRight="13dp"
                android:paddingBottom="12.5dp"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="5dp"
                android:text="@string/Turkish_lang"
                android:textAlignment="center"
                android:paddingHorizontal="13dp"
                android:paddingVertical="12.5dp"/>
                
            <TextView
                android:textSize="14sp"
                android:textColor="?attr/black"
                android:id="@+id/ru"
                android:background="@drawable/grey_block_line"
                android:paddingLeft="13dp"
                android:paddingTop="12.5dp"
                android:paddingRight="13dp"
                android:paddingBottom="12.5dp"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="5dp"
                android:text="@string/Russian_lang"
                android:textAlignment="center"
                android:paddingHorizontal="13dp"
                android:paddingVertical="12.5dp"/>
                
            <TextView
                android:textSize="14sp"
                android:textColor="?attr/black"
                android:id="@+id/ua"
                android:background="@drawable/grey_block_line"
                android:paddingLeft="13dp"
                android:paddingTop="12.5dp"
                android:paddingRight="13dp"
                android:paddingBottom="12.5dp"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="5dp"
                android:text="@string/Ukraine_lang"
                android:textAlignment="center"
                android:paddingHorizontal="13dp"
                android:paddingVertical="12.5dp"/>
                
            <TextView
                android:textSize="14sp"
                android:textColor="?attr/black"
                android:id="@+id/ar"
                android:background="@drawable/grey_block_line"
                android:paddingLeft="13dp"
                android:paddingTop="12.5dp"
                android:paddingRight="13dp"
                android:paddingBottom="12.5dp"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="5dp"
                android:text="@string/arabic_lang"
                android:textAlignment="center"
                android:paddingHorizontal="13dp"
                android:paddingVertical="12.5dp"/>
                
            <TextView
                android:textSize="14sp"
                android:textColor="?attr/black"
                android:id="@+id/zh"
                android:background="@drawable/grey_block_line_down"
                android:paddingLeft="13dp"
                android:paddingTop="12.5dp"
                android:paddingRight="13dp"
                android:paddingBottom="12.5dp"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="@string/_hinese_traditional_lang_res_0x7f130305"
                android:textAlignment="center"
                android:paddingHorizontal="13dp"
                android:paddingVertical="12.5dp"/>
        </LinearLayout>
    </ScrollView>
</LinearLayout>
