<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@color/white"
    android:padding="16dp">

    <!-- Header Section -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:layout_marginBottom="32dp"
        android:gravity="center">
        
        <TextView
            android:id="@+id/titleText"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/onboarding_slides_title"
            android:textSize="24sp"
            android:textColor="?attr/black"
            android:textStyle="bold"
            android:gravity="center"
            android:layout_marginBottom="8dp"/>
            
        <TextView
            android:id="@+id/subtitleText"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/onboarding_slides_subtitle"
            android:textSize="16sp"
            android:textColor="?attr/black"
            android:alpha="0.7"
            android:gravity="center"/>
    </LinearLayout>

    <!-- Content Area - Placeholder for existing onboarding slides -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:orientation="vertical"
        android:gravity="center">
        
        <!-- App Logo -->
        <LinearLayout
            android:orientation="vertical"
            android:background="@drawable/logo_start"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="24dp"/>
        
        <!-- Welcome Message -->
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/app_name"
            android:textSize="28sp"
            android:textColor="?attr/black"
            android:textStyle="bold"
            android:gravity="center"
            android:layout_marginBottom="8dp"/>
            
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/by_TJ"
            android:textSize="18sp"
            android:textColor="?attr/black"
            android:gravity="center"
            android:layout_marginBottom="32dp"/>
        
        <!-- Feature Highlights -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:layout_marginBottom="32dp">
            
            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="@string/onboarding_feature_1"
                android:textSize="16sp"
                android:textColor="?attr/black"
                android:gravity="center"
                android:layout_marginBottom="8dp"/>
                
            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="@string/onboarding_feature_2"
                android:textSize="16sp"
                android:textColor="?attr/black"
                android:gravity="center"
                android:layout_marginBottom="8dp"/>
                
            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="@string/onboarding_feature_3"
                android:textSize="16sp"
                android:textColor="?attr/black"
                android:gravity="center"/>
        </LinearLayout>
    </LinearLayout>

    <!-- Action Buttons -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center"
        android:layout_marginTop="16dp">
        
        <!-- Skip Button -->
        <Button
            android:id="@+id/skipOnboardingButton"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="@string/skip"
            android:textColor="?attr/black"
            android:background="@drawable/grey_block_line"
            android:layout_marginEnd="8dp"
            style="@style/Widget.AppCompat.Button.Borderless"/>
        
        <!-- Complete Button -->
        <Button
            android:id="@+id/completeOnboardingButton"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="@string/get_started"
            android:textColor="@color/white"
            android:background="@drawable/button_static"
            android:layout_marginStart="8dp"
            style="@style/Widget.AppCompat.Button.Borderless"/>
    </LinearLayout>
</LinearLayout>
