<?xml version="1.0" encoding="utf-8"?>
<navigation xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/onboarding_nav_graph"
    app:startDestination="@+id/languageSelectionFragment">

    <!-- Language Selection Fragment - First step in onboarding -->
    <fragment
        android:id="@+id/languageSelectionFragment"
        android:name="com.tqhit.battery.one.fragment.onboarding.LanguageSelectionFragment"
        android:label="@string/select_language_title"
        tools:layout="@layout/fragment_language_selection">
        
        <action
            android:id="@+id/action_languageSelection_to_onboarding"
            app:destination="@+id/onboardingSlidesFragment"
            app:enterAnim="@anim/slide_in_right"
            app:exitAnim="@anim/slide_out_left"
            app:popEnterAnim="@anim/slide_in_left"
            app:popExitAnim="@anim/slide_out_right" />
    </fragment>

    <!-- Onboarding Slides Fragment - Existing onboarding content -->
    <fragment
        android:id="@+id/onboardingSlidesFragment"
        android:name="com.tqhit.battery.one.fragment.onboarding.OnboardingSlidesFragment"
        android:label="@string/onboarding_slides_title"
        tools:layout="@layout/fragment_onboarding_slides">
        
        <action
            android:id="@+id/action_onboardingSlides_to_main"
            app:destination="@+id/mainActivity"
            app:enterAnim="@anim/fade_in"
            app:exitAnim="@anim/fade_out"
            app:popUpTo="@+id/onboarding_nav_graph"
            app:popUpToInclusive="true" />
    </fragment>

    <!-- Main Activity - Final destination after onboarding -->
    <activity
        android:id="@+id/mainActivity"
        android:name="com.tqhit.battery.one.activity.main.MainActivity"
        android:label="@string/app_name"
        tools:layout="@layout/activity_main" />

</navigation>
