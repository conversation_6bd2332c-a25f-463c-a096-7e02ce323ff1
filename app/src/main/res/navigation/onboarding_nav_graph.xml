<?xml version="1.0" encoding="utf-8"?>
<navigation xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/onboarding_nav_graph"
    app:startDestination="@+id/languageSelectionFragment">

    <!-- Language Selection Fragment - First step in onboarding -->
    <fragment
        android:id="@+id/languageSelectionFragment"
        android:name="com.tqhit.battery.one.fragment.onboarding.LanguageSelectionFragment"
        android:label="@string/select_language_title"
        tools:layout="@layout/fragment_language_selection">

        <action
            android:id="@+id/action_languageSelection_to_startingActivity"
            app:destination="@+id/startingActivity"
            app:enterAnim="@anim/fade_in"
            app:exitAnim="@anim/fade_out"
            app:popUpTo="@+id/onboarding_nav_graph"
            app:popUpToInclusive="true" />
    </fragment>

    <!-- Starting Activity - Original onboarding slides -->
    <activity
        android:id="@+id/startingActivity"
        android:name="com.tqhit.battery.one.activity.starting.StartingActivity"
        android:label="@string/app_name"
        tools:layout="@layout/activity_starting" />

</navigation>
